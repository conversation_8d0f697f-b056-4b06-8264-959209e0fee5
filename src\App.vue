<template>
  <a-config-provider :locale="antdLocale" :theme="themeConfig">
    <router-view v-slot="{ Component, route }">
      <component :is="Component" :key="route.path" />
    </router-view>
  </a-config-provider>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import enUS from 'ant-design-vue/es/locale/en_US';
import { theme } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';

const { locale } = useI18n();

// 根据当前语言配置dayjs
const setDayjsLocale = (currentLocale: string) => {
  if (currentLocale === 'en-US') {
    dayjs.locale('en');
  } else {
    dayjs.locale('zh-cn');
  }
};

// 初始化dayjs语言
setDayjsLocale(locale.value);

// 监听语言变化，同步更新dayjs语言
watch(locale, newLocale => {
  setDayjsLocale(newLocale);
});

// Ant Design Vue 语言包配置
const antdLocale = computed(() => {
  return locale.value === 'en-US' ? enUS : zhCN;
});

// 主题配置
const themeConfig = computed(() => ({
  token: {
    colorPrimary: '#1890ff', // 主题色
    borderRadius: 6, // 圆角
    fontSize: 14, // 字体大小
  },
  algorithm: theme.defaultAlgorithm, // 使用默认算法
}));
</script>

<style></style>
