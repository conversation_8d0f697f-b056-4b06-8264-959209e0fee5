<script setup lang="ts">
import { message, notification } from 'ant-design-vue';
import { UserOutlined } from '@ant-design/icons-vue';

// 响应式数据
const drawerVisible = ref(false);
const modalVisible = ref(false);

const formData = reactive({
  username: '',
  email: '',
});

const statistics = reactive({
  users: 1234,
  progress: 75,
});
// 表格数据
const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
  },
];

const tableData = ref([
  {
    key: '1',
    name: '张三',
    age: 32,
    address: '北京市朝阳区',
  },
  {
    key: '2',
    name: '李四',
    age: 28,
    address: '上海市浦东新区',
  },
  {
    key: '3',
    name: '王五',
    age: 35,
    address: '广州市天河区',
  },
]);

// 方法
const showDrawer = () => {
  drawerVisible.value = true;
};

const closeDrawer = () => {
  drawerVisible.value = false;
};

const showModal = () => {
  modalVisible.value = true;
};

const handleModalOk = () => {
  modalVisible.value = false;
};

const showMessage = () => {
  message.success('这是一条成功消息！');
};

const showNotification = () => {
  notification.info({
    message: '通知标题',
    description: '这是一条信息通知，展示了 Ant Design Vue 的通知组件。',
  });
};
</script>

<template>
  <div class="app-container">
    <a-layout class="min-h-screen">
      <!-- Header -->
      <a-layout-header class="bg-white shadow-sm">
        <div class="max-w-6xl mx-auto flex items-center justify-between h-full">
          <div class="flex items-center space-x-4">
            <h1 class="app-title">Vue3 + Ant Design Vue</h1>
          </div>
          <a-space>
            <a-button type="primary" @click="showDrawer">
              <template #icon>
                <UserOutlined />
              </template>
              用户中心
            </a-button>
          </a-space>
        </div>
      </a-layout-header>

      <!-- Main Content -->
      <a-layout-content class="content-wrapper">
        <div class="max-w-6xl mx-auto">
          <!-- Welcome Card -->
          <a-card
            class="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-0 shadow-md"
          >
            <template #title>
              <span
                class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
              >
                欢迎使用现代化前端开发脚手架
              </span>
            </template>
            <div class="text-gray-600">
              <p class="mb-2">
                🚀 技术栈：Vue 3 + TypeScript + Vite + Ant Design Vue + Sass
              </p>
              <p class="mb-2">📦 包管理：pnpm</p>
              <p>✨ 开箱即用的现代化开发环境</p>
            </div>
          </a-card>

          <!-- Demo Components -->
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :lg="8">
              <a-card title="表单组件" class="demo-card">
                <a-form layout="vertical">
                  <a-form-item label="用户名">
                    <a-input
                      v-model:value="formData.username"
                      placeholder="请输入用户名"
                    />
                  </a-form-item>
                  <a-form-item label="邮箱">
                    <a-input
                      v-model:value="formData.email"
                      placeholder="请输入邮箱"
                    />
                  </a-form-item>
                  <a-form-item>
                    <a-button type="primary" class="w-full">提交</a-button>
                  </a-form-item>
                </a-form>
              </a-card>
            </a-col>

            <a-col :xs="24" :sm="12" :lg="8">
              <a-card
                title="数据展示"
                class="h-full hover:shadow-lg transition-shadow"
              >
                <a-statistic
                  title="用户总数"
                  :value="statistics.users"
                  :precision="0"
                  class="mb-4"
                />
                <a-progress :percent="statistics.progress" />
                <div class="mt-4">
                  <a-tag color="blue">Vue 3</a-tag>
                  <a-tag color="green">TypeScript</a-tag>
                  <a-tag color="purple">Vite</a-tag>
                </div>
              </a-card>
            </a-col>

            <a-col :xs="24" :sm="12" :lg="8">
              <a-card
                title="操作面板"
                class="h-full hover:shadow-lg transition-shadow"
              >
                <a-space direction="vertical" class="w-full">
                  <a-button type="primary" block @click="showMessage">
                    显示消息
                  </a-button>
                  <a-button type="default" block @click="showNotification">
                    显示通知
                  </a-button>
                  <a-button type="dashed" block @click="showModal">
                    打开模态框
                  </a-button>
                </a-space>
              </a-card>
            </a-col>
          </a-row>

          <!-- Data Table -->
          <a-card title="数据表格" class="mt-6">
            <a-table
              :columns="columns"
              :data-source="tableData"
              :pagination="{ pageSize: 5 }"
              class="bg-white rounded-lg"
            />
          </a-card>
        </div>
      </a-layout-content>

      <!-- Footer -->
      <a-layout-footer class="text-center bg-gray-50 border-t">
        <div class="text-gray-500">
          Vue3 + Ant Design Vue ©2024 Created with ❤️
        </div>
      </a-layout-footer>
    </a-layout>

    <!-- Drawer -->
    <a-drawer
      title="用户中心"
      placement="right"
      :open="drawerVisible"
      @close="closeDrawer"
    >
      <div class="space-y-4">
        <a-avatar :size="64" class="mx-auto block">
          <template #icon><UserOutlined /></template>
        </a-avatar>
        <div class="text-center">
          <h3 class="text-lg font-medium">演示用户</h3>
          <p class="text-gray-500"><EMAIL></p>
        </div>
        <a-divider />
        <a-menu mode="vertical" class="border-0">
          <a-menu-item key="profile">个人资料</a-menu-item>
          <a-menu-item key="settings">系统设置</a-menu-item>
          <a-menu-item key="logout">退出登录</a-menu-item>
        </a-menu>
      </div>
    </a-drawer>

    <!-- Modal -->
    <a-modal v-model:open="modalVisible" title="示例模态框" @ok="handleModalOk">
      <p class="text-gray-600">
        这是一个使用 Ant Design Vue 组件的模态框示例。
      </p>
      <p class="text-gray-600">结合 Sass/SCSS 可以快速构建美观的用户界面。</p>
    </a-modal>
  </div>
</template>

<style scoped lang="scss">
// 使用 Sass 嵌套语法的自定义样式
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .ant-layout {
    background: transparent;

    &-header {
      position: sticky;
      top: 0;
      z-index: 100;
      backdrop-filter: blur(10px);
    }

    &-content {
      background: rgb(255, 255, 255, 10%);
      backdrop-filter: blur(10px);
    }
  }
}

// 卡片悬停效果
.ant-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}
</style>
