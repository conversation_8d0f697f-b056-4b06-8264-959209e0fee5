<template>
  <div class="i18n-demo">
    <a-card title="国际化演示" :bordered="false">
      <!-- 语言切换器 -->
      <div class="language-switcher">
        <a-space>
          <span>{{ t('settings.language') }}:</span>
          <LanguageSwitcher />
        </a-space>
      </div>

      <a-divider />

      <!-- 基础文本演示 -->
      <div class="demo-section">
        <h3>{{ t('common.operation') }}</h3>
        <a-space>
          <a-button type="primary">{{ t('common.confirm') }}</a-button>
          <a-button>{{ t('common.cancel') }}</a-button>
          <a-button>{{ t('common.submit') }}</a-button>
          <a-button>{{ t('common.reset') }}</a-button>
        </a-space>
      </div>

      <a-divider />

      <!-- 表单演示 -->
      <div class="demo-section">
        <h3>{{ t('login.title') }}</h3>
        <a-form :model="form" layout="vertical" style="max-width: 400px">
          <a-form-item :label="t('login.username')">
            <a-input v-model:value="form.username" :placeholder="t('login.username')" />
          </a-form-item>
          <a-form-item :label="t('login.password')">
            <a-input-password v-model:value="form.password" :placeholder="t('login.password')" />
          </a-form-item>
          <a-form-item>
            <a-checkbox v-model:checked="form.remember">
              {{ t('login.rememberMe') }}
            </a-checkbox>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">
              {{ t('login.loginBtn') }}
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <a-divider />

      <!-- 菜单演示 -->
      <div class="demo-section">
        <h3>{{ t('menu.home') }}</h3>
        <a-menu mode="horizontal">
          <a-menu-item key="1">{{ t('menu.home') }}</a-menu-item>
          <a-menu-item key="2">{{ t('menu.applications') }}</a-menu-item>
          <a-menu-item key="3">{{ t('menu.knowledgeBase') }}</a-menu-item>
          <a-menu-item key="4">{{ t('menu.modelLibrary') }}</a-menu-item>
          <a-menu-item key="5">{{ t('menu.appSquare') }}</a-menu-item>
        </a-menu>
      </div>

      <a-divider />

      <!-- 消息演示 -->
      <div class="demo-section">
        <h3>{{ t('message.info') }}</h3>
        <a-space>
          <a-button @click="showMessage('success')">{{ t('message.success') }}</a-button>
          <a-button @click="showMessage('error')">{{ t('message.error') }}</a-button>
          <a-button @click="showMessage('warning')">{{ t('message.warning') }}</a-button>
          <a-button @click="showMessage('info')">{{ t('message.info') }}</a-button>
        </a-space>
      </div>

      <a-divider />

      <!-- 当前语言信息 -->
      <div class="demo-section">
        <h3>当前语言信息</h3>
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="当前语言">
            {{ locale }}
          </a-descriptions-item>
          <a-descriptions-item label="是否中文">
            {{ isZhCN() ? '是' : '否' }}
          </a-descriptions-item>
          <a-descriptions-item label="是否英文">
            {{ isEnUS() ? 'Yes' : 'No' }}
          </a-descriptions-item>
          <a-descriptions-item label="当前时间">
            {{ currentTime }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useI18n } from '@/hooks/useI18n';
import LanguageSwitcher from '@/components/LanguageSwitcher/index.vue';

const { t, locale, isZhCN, isEnUS } = useI18n();

// 表单数据
const form = ref({
  username: '',
  password: '',
  remember: false,
});

// 当前时间
const currentTime = computed(() => {
  return dayjs().format('YYYY-MM-DD HH:mm:ss');
});

// 显示消息
const showMessage = (type: 'success' | 'error' | 'warning' | 'info') => {
  message[type](t(`message.${type}`));
};
</script>

<style scoped>
.i18n-demo {
  padding: 24px;
}

.language-switcher {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.demo-section {
  margin: 24px 0;
}

.demo-section h3 {
  margin-bottom: 16px;
  color: #1890ff;
}
</style>
