<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>智能体平台</h1>
        <p>欢迎登录管理系统</p>
      </div>

      <a-form :model="loginForm" layout="vertical" class="login-form" @finish="handleLogin">
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名' }]"
        >
          <a-input v-model:value="loginForm.username" placeholder="请输入用户名" size="large" />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <a-input-password
            v-model:value="loginForm.password"
            placeholder="请输入密码"
            size="large"
          />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit" block size="large" :loading="loading">
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

const router = useRouter();
const loading = ref(false);

const loginForm = reactive({
  username: '',
  password: '',
});

const handleLogin = async () => {
  loading.value = true;

  try {
    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 简单的模拟验证
    if (loginForm.username && loginForm.password) {
      message.success('登录成功');
      // 存储登录状态
      localStorage.setItem('token', 'demo-token');
      localStorage.setItem('username', loginForm.username);
      // 跳转到主页
      router.push('/');
    } else {
      message.error('用户名或密码不能为空');
    }
  } catch (_error) {
    message.error('登录失败');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h1 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 28px;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-form {
  width: 100%;
}
</style>
