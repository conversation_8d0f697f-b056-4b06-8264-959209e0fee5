# Vue3 + Ant Design Vue 脚手架

这是一个现代化的前端开发脚手架，集成了当前最流行的技术栈，为开发者提供开箱即用的开发环境。

## 🚀 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供静态类型检查
- **Vite** - 下一代前端构建工具
- **Ant Design Vue** - 企业级 UI 组件库
- **UnoCSS** - 即时按需原子化 CSS 引擎
- **Sass/SCSS** - CSS 预处理器
- **pnpm** - 快速、节省磁盘空间的包管理器

## 📦 安装

确保你的环境中已安装 Node.js >= 16 和 pnpm。

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 预览生产构建
pnpm preview

# 类型检查
pnpm type-check
```

## 🏗️ 项目结构

```
src/
├── components/          # 可复用组件
├── assets/             # 静态资源
├── styles/             # 样式文件
│   └── index.scss      # 主样式文件 (全局样式)
├── App.vue             # 根组件
└── main.ts             # 应用入口

public/                 # 公共文件
├── favicon.ico
└── ...

配置文件/

├── postcss.config.js   # PostCSS 配置
├── vite.config.ts      # Vite 配置
├── tsconfig.json       # TypeScript 配置
└── package.json        # 项目配置和依赖
```

## 🎨 特性

### 1. 组件库集成

- 完整的 Ant Design Vue 组件库
- 预配置的主题和样式
- 图标库支持

### 2. 样式解决方案

- **UnoCSS** 原子化 CSS 引擎
  - 完全兼容 Tailwind CSS 语法
  - 即时按需生成，零配置启动
  - 高性能，编译时优化
  - 支持属性化模式和图标预设
- **Sass/SCSS** 预处理器
- 响应式设计支持
- 自定义主题变量

### 3. 开发体验

- 热模块替换 (HMR)
- TypeScript 支持
- 代码分割
- 快速构建

### 4. 示例功能

- 响应式布局
- 表单处理
- 数据表格
- 模态框和抽屉
- 消息通知
- 路由准备 (可扩展)

## 🛠️ 配置说明

### 样式架构

- **src/styles/index.scss**: 主样式文件，包含全局样式
- **组件级样式**: 使用 `<style scoped lang="scss">` 进行样式隔离

### UnoCSS 配置

本项目使用 **UnoCSS** 作为原子化 CSS 引擎，提供了以下特性：

#### UnoCSS vs Tailwind CSS

| 特性         | UnoCSS                       | Tailwind CSS         |
| ------------ | ---------------------------- | -------------------- |
| **性能**     | 编译时按需生成，极快的冷启动 | 运行时 JIT，启动稍慢 |
| **语法兼容** | 100% 兼容 Tailwind 语法      | Tailwind 原生语法    |
| **体积**     | 更小的运行时开销             | 较大的初始包体积     |
| **配置**     | 零配置启动，高度可定制       | 需要配置文件         |
| **预设**     | 丰富的预设生态               | 官方预设             |

#### 使用方式

你可以直接使用 Tailwind CSS 的所有语法：

```html
<!-- 完全兼容 Tailwind CSS 语法 -->
<div
  class="flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg"
>
  内容
</div>

<!-- 属性化模式 (UnoCSS 特有) -->
<div flex items-center justify-center p-4 bg-blue-500 text-white rounded-lg>
  内容
</div>
```

#### 预定义快捷方式

项目已预配置常用样式组合：

```html
<div class="flex-center">居中对齐</div>
<div class="flex-between">两端对齐</div>
<div class="flex-col-center">垂直居中</div>
<div class="absolute-center">绝对定位居中</div>
```

#### 配置文件

- **uno.config.ts**: UnoCSS 主配置文件
- **vite/plugins/unocss.ts**: Vite 插件配置

### 主题定制

可以通过以下方式自定义主题：

1. **UnoCSS 主题**: 在 `uno.config.ts` 中修改 theme 配置
2. **Ant Design Vue 主题**: 修改 CSS 变量
3. **Sass/SCSS**: 在 `src/styles/index.scss` 中添加自定义样式

## 📝 使用示例

### 创建新组件

```vue
<template>
  <!-- 使用 UnoCSS 原子化样式 -->
  <div class="p-6 bg-white rounded-lg shadow-md">
    <a-button type="primary" class="mb-4"> Ant Design 按钮 </a-button>

    <!-- 使用预定义快捷方式 -->
    <div class="flex-center mb-4 p-4 bg-blue-50 rounded">
      <span class="text-blue-600 font-medium">使用 UnoCSS 快捷方式</span>
    </div>

    <!-- 属性化模式 (可选) -->
    <div flex items-center gap-2 p-2 border border-gray-200 rounded>
      <div class="w-4 h-4 bg-green-500 rounded-full"></div>
      <span>属性化模式语法</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style scoped lang="scss">
// 如需自定义样式，仍可使用 SCSS
.custom-style {
  // 自定义样式
}
</style>
```

### 使用响应式设计

```vue
<template>
  <!-- Ant Design Vue 网格系统 -->
  <a-row :gutter="[16, 16]">
    <a-col :xs="24" :sm="12" :lg="8">
      <div class="p-4 bg-blue-50 rounded-lg">响应式卡片</div>
    </a-col>
  </a-row>

  <!-- UnoCSS 响应式语法 (兼容 Tailwind) -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
    <div class="p-4 bg-gray-100 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">卡片 1</h3>
      <p class="text-gray-600">使用 UnoCSS 网格系统</p>
    </div>
    <div class="p-4 bg-gray-100 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">卡片 2</h3>
      <p class="text-gray-600">完全兼容 Tailwind 语法</p>
    </div>
  </div>
</template>
```

## 🔧 常用命令

```bash
# 安装新依赖
pnpm add <package-name>

# 安装开发依赖
pnpm add -D <package-name>

# 更新依赖
pnpm update

# 检查依赖
pnpm audit

# 清理缓存
pnpm store prune
```

## 📚 相关文档

- [Vue 3 官方文档](https://vuejs.org/)
- [Ant Design Vue](https://antdv.com/)
- [UnoCSS](https://unocss.dev/) - 原子化 CSS 引擎
- [Tailwind CSS](https://tailwindcss.com/) - 语法参考
- [Vite](https://vitejs.dev/)
- [TypeScript](https://www.typescriptlang.org/)
- [pnpm](https://pnpm.io/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个脚手架！

## �� 许可证

MIT License
