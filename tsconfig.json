{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "strict": true, "noLib": false, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": false, "jsx": "preserve", "baseUrl": ".", "allowJs": true, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["dom", "dom.iterable", "esnext"], "noImplicitAny": false, "skipLibCheck": true, "isolatedModules": true, "allowImportingTsExtensions": true, "noEmit": true, "types": ["vite/client", "@vueuse/core"], "removeComments": true, "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "tests/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts", "uno.config.ts"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"], "ts-node": {"esm": true}}