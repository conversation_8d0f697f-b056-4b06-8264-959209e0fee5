//对外暴露配置路由(常量路由):全部用户都可以访问到的路由
export const homeRoute = [
  {
    path: '/app-square',
    component: () => import('@/views/home/<USER>/index.vue'),
    name: 'app-square',
    meta: {
      title: '应用广场',
    },
  },
  {
    path: '/model-library',
    component: () => import('@/views/home/<USER>/index.vue'),
    name: 'model-library',
    meta: {
      title: '模型库',
    },
  },
  {
    path: '/applications',
    component: () => import('@/views/home/<USER>/index.vue'),
    name: 'applications',
    meta: {
      title: '应用',
    },
  },
  {
    path: '/knowledge-base',
    component: () => import('@/views/home/<USER>/index.vue'),
    name: 'knowledge-base',
    meta: {
      title: '知识库',
    },
  },
  {
    path: '/i18n-demo',
    component: () => import('@/views/demo/i18n-demo/index.vue'),
    name: 'i18n-demo',
    meta: {
      title: '国际化示例',
    },
  },

  {
    path: '/api-demo',
    component: () => import('@/views/demo/api-demo/index.vue'),
    name: 'api-demo',
    meta: {
      title: '接口调用示例',
    },
  },
];

export const constantRoute = [
  {
    //登录
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    name: 'login',
    meta: {
      title: '登录', //菜单标题
      hidden: true, //代表路由标题在菜单中是否隐藏  true:隐藏 false:不隐藏
    },
  },
  {
    //主布局
    path: '/',
    component: () => import('@/views/home/<USER>'),
    name: 'layout',
    redirect: '/app-square',
    children: homeRoute,
  },
];

//异步路由
export const asnycRoute = [];

//任意路由
export const anyRoute = {
  //任意路由
  path: '/:pathMatch(.*)*',
  redirect: '/app-square',
  name: 'Any',
  meta: {
    title: '任意路由',
    hidden: true,
  },
};
