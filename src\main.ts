import { createApp } from 'vue';
import Antd from 'ant-design-vue';
import App from './App.vue';
import globalComponent from './components/index';
import 'ant-design-vue/dist/reset.css';
import 'uno.css'; // UnoCSS 样式
import './styles/index.scss';
//引入路由
import router from './router';
//引入仓库
import pinia from './store';
//引入国际化
import i18n from './i18n';

const app = createApp(App);
//安装自定义插件
app.use(globalComponent);
app.use(Antd);
//安装仓库
app.use(pinia);
//注册模板路由
app.use(router);
//安装国际化
app.use(i18n);
//引入路由鉴权文件
// import '@/router/permission';
//将应用挂载到挂载点上
app.mount('#app');
