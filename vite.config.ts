import path from 'path';
import type { ConfigEnv } from 'vite';
import { createVitePlugins } from './vite/plugins';
import proxy from './vite/proxy';
import { VITE_PORT } from './vite/constant';

// https://vitejs.dev/config/
export default ({ command }: ConfigEnv) => {
  // 获取各种环境下的对应的变量
  const isBuild = command === 'build';
  const root = process.cwd();

  // 加载环境变量（可用于后续扩展）
  // const env = loadEnv(mode, root);

  return {
    root,
    base: '/',
    plugins: createVitePlugins(isBuild),

    // 路径别名配置
    resolve: {
      alias: {
        '@': path.resolve('./src'),
        '@home': path.resolve('./src/views/home/'),
        '@components': path.resolve('./src/components/'),
        '@utils': path.resolve('./src/utils/'),
        '@assets': path.resolve('./src/assets/'),
        '@api': path.resolve('./src/api/'),
        '@stores': path.resolve('./src/stores/'),
        '@types': path.resolve('./src/types/'),
      },
    },

    // 环境变量配置
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    },

    // CSS 配置
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          javascriptEnabled: true,
        },
      },
      devSourcemap: !isBuild,
    },

    // 依赖预构建优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'ant-design-vue/es',
      ],
      exclude: ['vue-demi'],
    },

    // 开发服务器配置
    server: {
      hmr: {
        overlay: false,
        port: 24678, // HMR 专用端口
      },
      port: VITE_PORT,
      open: false,
      cors: true,
      host: '0.0.0.0',
      proxy,
      // 优化文件监听
      watch: {
        usePolling: false, // 改为 false，提高性能
        ignored: ['**/node_modules/**', '**/.git/**'],
      },
    },

    // 构建配置
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isBuild,
          drop_debugger: isBuild,
          pure_funcs: isBuild ? ['console.log'] : [],
        },
      },
      sourcemap: !isBuild,
      chunkSizeWarningLimit: 1000,

      // Rollup 配置
      rollupOptions: {
        output: {
          // 更智能的分包策略
          manualChunks(id) {
            // 大型UI库单独分包
            if (id.includes('ant-design-vue')) {
              return 'vendor-antd';
            }
            if (id.includes('element-plus')) {
              return 'vendor-element';
            }

            // Vue 生态系统分包
            if (id.includes('vue-router')) {
              return 'vendor-router';
            }
            if (id.includes('pinia')) {
              return 'vendor-store';
            }
            if (id.includes('vue') || id.includes('@vue')) {
              return 'vendor-vue';
            }

            // 工具库分包
            if (id.includes('@vueuse/core')) {
              return 'vendor-vueuse';
            }
            if (
              id.includes('lodash') ||
              id.includes('dayjs') ||
              id.includes('axios')
            ) {
              return 'vendor-utils';
            }

            // 其他大型第三方库分包
            if (id.includes('node_modules')) {
              const packageName = id.split('node_modules/')[1].split('/')[0];
              const largePackages = [
                'echarts',
                'monaco-editor',
                'three',
                'pdf',
                'codemirror',
              ];

              if (largePackages.some(pkg => packageName.includes(pkg))) {
                return `vendor-${packageName}`;
              }

              // 其他小型依赖合并到vendor包
              return 'vendor';
            }
          },

          // 文件名配置
          chunkFileNames: 'js/[name]-[hash:8].js',
          entryFileNames: 'js/[name]-[hash:8].js',
          assetFileNames: assetInfo => {
            const extType = assetInfo.name?.split('.').pop()?.toLowerCase();
            const imgExts = ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'ico'];
            const fontExts = ['woff', 'woff2', 'eot', 'ttf', 'otf'];

            if (imgExts.includes(extType || '')) {
              return 'images/[name]-[hash:8].[ext]';
            }
            if (fontExts.includes(extType || '')) {
              return 'fonts/[name]-[hash:8].[ext]';
            }
            if (extType === 'css') {
              return 'css/[name]-[hash:8].[ext]';
            }
            return 'assets/[name]-[hash:8].[ext]';
          },
        },

        // 外部依赖配置（可选）
        external: [],
      },

      // 报告压缩后的大小
      reportCompressedSize: isBuild,

      // 构建时清空输出目录
      emptyOutDir: true,
    },

    // 预览服务器配置
    preview: {
      port: 4173,
      host: '0.0.0.0',
      cors: true,
    },
  };
};
