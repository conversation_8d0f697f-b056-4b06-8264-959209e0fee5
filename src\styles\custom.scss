@use './variable';

// ===== 现代美观滚动条样式 =====

// 主滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: transparent;
}

// 滚动条轨道
::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 3px;
  margin: 2px;
}

// 滚动条滑块
::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.15) 100%);
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease-in-out;

  // 悬停状态
  &:hover {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0.25) 100%);
    transform: scaleY(1.1);
  }

  // 激活状态
  &:active {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.45) 0%, rgba(0, 0, 0, 0.35) 100%);
  }
}

// 滚动条交汇处
::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.04);
}

// ===== 特殊场景滚动条样式 =====

// 深色主题滚动条
[data-theme='dark'] {
  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.06);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
    border: 1px solid rgba(0, 0, 0, 0.1);

    &:hover {
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.45) 0%,
        rgba(255, 255, 255, 0.35) 100%
      );
    }

    &:active {
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.55) 0%,
        rgba(255, 255, 255, 0.45) 100%
      );
    }
  }

  ::-webkit-scrollbar-corner {
    background: rgba(255, 255, 255, 0.06);
  }
}

// 精简滚动条（适用于对话框、下拉菜单等）
.scrollbar-thin {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    border: none;

    &:hover {
      background: rgba(0, 0, 0, 0.25);
    }
  }
}

// 宽滚动条（适用于大型内容区域）
.scrollbar-wide {
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.06);
    border-radius: 6px;
    margin: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.15) 100%);
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);

    &:hover {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.3) 100%);
      transform: scale(1.05);
    }
  }
}

// ===== 移动端滚动优化 =====
@media (max-width: 768px) {
  // 移动端隐藏滚动条但保持可滚动性
  .scrollbar-mobile-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }

  // 移动端超细滚动条
  ::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 1px;
    border: none;
  }
}

// ===== 滚动条动画效果 =====
@keyframes scrollbar-appear {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

// 滚动时显示滚动条
.scrollbar-auto-hide {
  ::-webkit-scrollbar-thumb {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  &:hover ::-webkit-scrollbar-thumb,
  &:focus ::-webkit-scrollbar-thumb,
  &.scrolling ::-webkit-scrollbar-thumb {
    opacity: 1;
    animation: scrollbar-appear 0.2s ease-out;
  }
}
