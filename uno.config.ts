import {
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
} from "unocss";

export default defineConfig({
  presets: [
    presetUno(), // 基础原子化 CSS
    presetAttributify(), // 属性化模式
    presetIcons({
      scale: 1.2,
      warn: true,
    }), // 图标预设
  ],
  shortcuts: [
    // 常用组合样式
    {
      "flex-center": "flex items-center justify-center",
      "flex-between": "flex items-center justify-between",
      "flex-col-center": "flex flex-col items-center justify-center",
      "absolute-center":
        "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
    },
  ],
  theme: {
    colors: {
      primary: {
        50: "#eff6ff",
        100: "#dbeafe",
        500: "#3b82f6",
        600: "#2563eb",
        700: "#1d4ed8",
      },
    },
  },
});
