@use './custom';

// ===== 基础重置样式 =====
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
  margin: 0;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: 400;
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  text-align: left;
  background-color: var(--color-bg-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ===== 排版样式 =====
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  color: var(--color-text-primary);
  font-weight: 500;
  line-height: var(--line-height-tight);
}

h1 {
  font-size: 38px;
}
h2 {
  font-size: 30px;
}
h3 {
  font-size: 24px;
}
h4 {
  font-size: 20px;
}
h5 {
  font-size: 16px;
}
h6 {
  font-size: 14px;
}

p {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

// ===== 链接样式 =====
a {
  color: var(--color-primary);
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  transition: var(--transition-base);

  &:hover {
    color: var(--color-primary-hover);
  }

  &:active {
    color: var(--color-primary-active);
  }

  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}
