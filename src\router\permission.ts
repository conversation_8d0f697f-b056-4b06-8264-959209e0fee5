// //路由鉴权:鉴权,项目当中路由能不能被的权限的设置(某一个路由什么条件下可以访问、什么条件下不可以访问)
// import router from '@/router';
// import nprogress from 'nprogress';
// import useUserStore from '@/store/modules/user';
// import pinia from '@/store/index';
// import { REMOVE_TOKEN } from '@/utils/token';
// //引入进度条样式
// import 'nprogress/nprogress.css';

// const setting = {
//   title: 'xxxx', //项目的标题
//   // logo: '/logo.png',//项目logo设置
//   logoHidden: true, //logo组件是否隐藏设置
// };
// nprogress.configure({ showSpinner: false });
// //获取用户相关的小仓库内部token数据,去判断用户是否登录成功
// const userStore = useUserStore(pinia);

// //全局守卫:项目当中任意路由切换都会触发的钩子
// //全局前置守卫
// router.beforeEach(async (to: any, from: any, next: any) => {
//   document.title = `${setting.title} - ${to.meta.title}`;

//   nprogress.start();
//   const token = userStore.token;
//   const userName = userStore.userName;

//   if (token) {
//     //登录成功,访问login,不能访问,指向首页
//     if (to.path == '/login') {
//       next({ path: '/' });
//     } else {
//       //登录成功访问其余六个路由(登录排除)
//       //有用户信息
//       if (userName) {
//         //放行
//         next();
//       } else {
//         //如果没有用户信息,在守卫这里发请求获取到了用户信息再放行
//         try {
//           //获取用户信息
//           await userStore.getUserInfo();
//           //放行
//           //万一:刷新的时候是异步路由,有可能获取到用户信息、异步路由还没有加载完毕,出现空白的效果
//           next({ ...to });
//         } catch (error: any) {
//           console.log('🚀 ~ file: permission.ts:48 ~ router.beforeEach ~ error:', error);
//           // ElMessage.error(String(error));
//           //token过期:获取不到用户信息了
//           //用户手动修改本地存储token
//           //退出登录->用户相关的数据清空
//           const res = await userStore.userLogout();
//           if (!res.success) {
//             REMOVE_TOKEN();
//           }
//           next({ path: '/login', query: { redirect: to.path } });
//         }
//       }
//     }
//   } else {
//     //用户未登录判断
//     if (to.path == '/login') {
//       next();
//     } else {
//       next({ path: '/login', query: { redirect: to.path } });
//     }
//   }
// });
// //全局后置守卫
// router.afterEach(() => {
//   nprogress.done();
// });
