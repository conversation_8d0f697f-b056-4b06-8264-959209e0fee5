<template>
  <div class="admin-layout">
    <a-layout class="min-h-screen">
      <!-- 左侧菜单 -->
      <a-layout-sider v-model:collapsed="collapsed" theme="dark" width="240" collapsible>
        <!-- Logo区域 -->
        <div class="logo">
          <h2>{{ t('menu.home') }}</h2>
        </div>

        <!-- 菜单 -->
        <a-menu
          v-model:selected-keys="selectedKeys"
          mode="inline"
          theme="dark"
          @click="handleMenuClick"
        >
          <a-menu-item key="/app-square">
            <template #icon>
              <AppstoreOutlined />
            </template>
            <span>应用广场</span>
          </a-menu-item>

          <a-menu-item key="/model-library">
            <template #icon>
              <DatabaseOutlined />
            </template>
            <span>模型库</span>
          </a-menu-item>

          <a-menu-item key="/applications">
            <template #icon>
              <RocketOutlined />
            </template>
            <span>应用</span>
          </a-menu-item>

          <a-menu-item key="/knowledge-base">
            <template #icon>
              <BookOutlined />
            </template>
            <span>知识库</span>
          </a-menu-item>
          <a-menu-item key="/i18n-demo">
            <template #icon>
              <GlobalOutlined />
            </template>
            <span>国际化示例</span>
          </a-menu-item>
        </a-menu>
      </a-layout-sider>

      <!-- 右侧内容区域 -->
      <a-layout>
        <!-- 头部 -->
        <a-layout-header class="header">
          <div class="header-left">
            <a-button type="text" @click="toggleCollapsed">
              <MenuUnfoldOutlined v-if="collapsed" />
              <MenuFoldOutlined v-else />
            </a-button>
          </div>

          <div class="header-right">
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="handleLogout">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="text">
                <UserOutlined />
                <span class="username">{{ username }}</span>
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </div>
        </a-layout-header>

        <!-- 主内容区域 -->
        <a-layout-content class="content">
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  AppstoreOutlined,
  DatabaseOutlined,
  RocketOutlined,
  BookOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  DownOutlined,
  GlobalOutlined,
} from '@ant-design/icons-vue';
import { useI18n } from '@/hooks/useI18n';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const collapsed = ref(false);
const selectedKeys = ref(['/app-square']);
const username = ref('管理员');

const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};

// @ts-ignore: Ant Design Vue 类型定义兼容性问题
const handleMenuClick = info => {
  selectedKeys.value = [info.key as string];
  router.push(info.key as string);
};

const handleLogout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('username');
  message.success('退出登录成功');
  router.push('/login');
};

onMounted(() => {
  // 检查登录状态
  const token = localStorage.getItem('token');
  const storedUsername = localStorage.getItem('username');

  if (!token) {
    router.push('/login');
    return;
  }

  if (storedUsername) {
    username.value = storedUsername;
  }

  // 根据当前路由设置选中的菜单
  const currentPath = route.path;
  if (currentPath !== '/') {
    selectedKeys.value = [currentPath];
  } else {
    // 默认跳转到应用广场
    router.push('/app-square');
  }
});
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 6px;
}

.logo h2 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  text-align: center;
}

.header {
  background: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left button {
  font-size: 16px;
}

.header-right {
  display: flex;
  align-items: center;
}

.username {
  margin-left: 8px;
  margin-right: 4px;
}

.content {
  margin: 24px;
  padding: 24px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: calc(100vh - 64px - 48px);
}
</style>
