{"name": "ai-agent-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "build:analyze": "VITE_ANALYSIS=true vite build", "build:report": "node scripts/build-analyzer.js", "clean": "rimraf dist node_modules/.vite", "preview:dist": "vite preview --port 4173", "lint": "eslint . --fix", "lint:check": "eslint .", "lint:style": "stylelint \"src/**/*.{css,scss,vue}\" --fix", "lint:style:check": "stylelint \"src/**/*.{css,scss,vue}\"", "format": "prettier --write .", "format:check": "prettier --check .", "lint:all": "run-p lint:check lint:style:check format:check type-check"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "dayjs": "^1.11.13", "vue": "^3.5.13", "vue-i18n": "9"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tsconfig/node22": "^22.0.1", "@types/eslint": "^9.6.1", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@unocss/vite": "^66.2.3", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^13.3.0", "autoprefixer": "^10.4.21", "eslint-config-prettier": "^10.1.5", "eslint-define-config": "^2.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-vue": "^10.2.0", "fast-glob": "^3.3.3", "globals": "^16.2.0", "npm-run-all2": "^7.0.2", "pinia": "^3.0.3", "postcss": "^8.5.6", "prettier": "^3.5.3", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "stylelint": "^16.21.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^7.0.0", "stylelint-scss": "^6.12.1", "typescript": "~5.8.3", "unocss": "^66.2.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.22.3", "vite-plugin-checker": "^0.9.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-restart": "^0.4.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-router": "^4.5.1", "vue-tsc": "^2.2.8"}}